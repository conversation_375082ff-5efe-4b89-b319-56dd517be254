#!/usr/bin/env python3
"""
测试上传图片到 Ideogram AI
基于 upload.md 中的接口信息
"""

import sys
import requests
import json
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.config import ConfigManager


def extract_bearer_token_from_cookies(cookies_str):
    """从 cookies 字符串中提取 Bearer token"""
    # 查找 session_cookie
    for cookie in cookies_str.split(';'):
        cookie = cookie.strip()
        if cookie.startswith('session_cookie='):
            # 提取 JWT token
            token = cookie.split('=', 1)[1]
            return token
    return None


def test_upload_image():
    """测试上传图片功能"""
    print("🖼️  测试上传图片到 Ideogram AI")
    print("=" * 50)

    try:
        # 加载配置
        config = ConfigManager('config.json')

        # 图片文件路径
        image_path = Path("121.png")
        if not image_path.exists():
            print(f"❌ 图片文件不存在: {image_path}")
            return False

        print(f"📁 图片文件: {image_path}")
        print(f"📏 文件大小: {image_path.stat().st_size} bytes")

        # 从 cookies 中提取 Bearer token
        cookies_str = config.get('cookies', '')
        bearer_token = extract_bearer_token_from_cookies(cookies_str)

        if not bearer_token:
            print("❌ 无法从 cookies 中提取 Bearer token")
            return False

        print(f"🔑 Bearer token 长度: {len(bearer_token)} 字符")

        # 创建会话并设置所有 cookies
        session = requests.Session()

        # 解析并设置所有 cookies
        cookies = {}
        for cookie in cookies_str.split(';'):
            cookie = cookie.strip()
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                cookies[name.strip()] = value.strip()
                session.cookies.set(name.strip(), value.strip())

        print(f"🍪 已设置 {len(cookies)} 个 cookies")

        # 准备上传请求
        url = "https://ideogram.ai/api/reference-collections/createReferenceCollection"

        # 设置完整的请求头（基于 upload.md 和浏览器标准）
        headers = {
            "accept": "*/*",
            "accept-language": "en-US,en;q=0.9",
            "authorization": f"Bearer {bearer_token}",
            "cache-control": "no-cache",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "referer": "https://ideogram.ai/character",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "sec-ch-ua-arch": '"arm"',
            "sec-ch-ua-bitness": '"64"',
            "sec-ch-ua-full-version": '"138.0.7204.184"',
            "sec-ch-ua-full-version-list": '"Not)A;Brand";v="*******", "Chromium";v="138.0.7204.184", "Google Chrome";v="138.0.7204.184"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-model": '""',
            "sec-ch-ua-platform": '"macOS"',
            "sec-ch-ua-platform-version": '"15.5.0"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": config.get('user_agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
            "x-ideo-org": "qyLmwvOwTmeJyuEDXKDJiw",  # 从 upload.md 复制
            "x-request-id": "test-upload-request-id"
        }
        
        # 准备 multipart/form-data
        with open(image_path, 'rb') as image_file:
            files = {
                'name': (None, '_NO_NAME_'),
                'reference_type': (None, 'CHARACTER'),
                'asset_blobs': (image_path.name, image_file.read(), 'image/png')
            }

            print("🚀 发送上传请求...")
            print(f"📍 URL: {url}")

            # 使用会话发送请求
            response = session.post(
                url,
                headers=headers,
                files=files,
                timeout=30
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📏 响应内容长度: {len(response.text)} 字符")
            
            # 显示响应头
            print("\n📋 响应头:")
            for key, value in response.headers.items():
                print(f"   {key}: {value}")
            
            # 显示响应内容
            print(f"\n📄 响应内容:")
            if response.headers.get('content-type', '').startswith('application/json'):
                try:
                    json_data = response.json()
                    print(json.dumps(json_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print(response.text[:500])
            else:
                print(response.text[:500])
            
            # 判断结果
            if response.status_code == 200:
                print("\n✅ 上传成功！")
                return True
            elif response.status_code == 201:
                print("\n✅ 上传成功（已创建）！")
                return True
            elif response.status_code == 401:
                print("\n❌ 认证失败，Bearer token 可能无效")
                return False
            elif response.status_code == 403:
                print("\n❌ 权限不足")
                return False
            elif response.status_code == 400:
                print("\n❌ 请求参数错误")
                return False
            else:
                print(f"\n⚠️  意外的状态码: {response.status_code}")
                return False
                
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 网络连接错误")
        return False
    except Exception as e:
        print(f"❌ 上传过程中出错: {e}")
        return False


def analyze_upload_requirements():
    """分析上传接口的要求"""
    print("\n🔍 上传接口分析:")
    print("=" * 50)
    
    requirements = {
        "URL": "https://ideogram.ai/api/reference-collections/createReferenceCollection",
        "方法": "POST",
        "认证": "Bearer Token (从 session_cookie 提取)",
        "内容类型": "multipart/form-data",
        "必需字段": {
            "name": "集合名称 (可以是 '_NO_NAME_')",
            "reference_type": "引用类型 ('CHARACTER')",
            "asset_blobs": "图片文件数据"
        },
        "特殊头部": {
            "x-ideo-org": "组织ID",
            "x-request-id": "请求追踪ID"
        }
    }
    
    for key, value in requirements.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for k, v in value.items():
                print(f"  {k}: {v}")
        else:
            print(f"{key}: {value}")


def main():
    """主函数"""
    print("🧪 Ideogram AI 图片上传测试")
    print("=" * 60)
    
    # 分析上传要求
    analyze_upload_requirements()
    
    # 测试上传
    success = test_upload_image()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 上传测试成功！")
        print("✅ 图片已成功上传到 Ideogram AI")
    else:
        print("❌ 上传测试失败！")
        print("\n🔧 可能的解决方案:")
        print("1. 检查 Bearer token 是否有效")
        print("2. 确认 cookies 是否已过期")
        print("3. 验证网络连接")
        print("4. 检查图片文件格式和大小")
        print("5. 确认 x-ideo-org 参数是否正确")


if __name__ == '__main__':
    main()
