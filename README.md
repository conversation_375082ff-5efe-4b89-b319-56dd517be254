# Ideogram AI 图片爬虫

一个用于爬取 Ideogram AI 生成图片的 Python 爬虫工具。

## 功能特性

- 🔐 Cookie 认证登录
- 🖼️ 批量图片下载
- ⚡ 异步并发下载
- 📁 自动文件管理
- 🛡️ 错误处理和重试机制
- 📊 下载进度显示

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. 编辑 `config.json` 文件，更新您的 cookie 信息
2. 设置下载目录和其他参数

## 使用方法

```bash
# 基本使用
python main.py

# 指定下载数量
python main.py --limit 50

# 指定关键词搜索
python main.py --keyword "anime"

# 显示帮助
python main.py --help
```

## 项目结构

```
ideogram爬虫/
├── main.py              # 主程序入口
├── config.json          # 配置文件
├── requirements.txt     # 依赖包
├── src/
│   ├── __init__.py
│   ├── auth.py          # 认证模块
│   ├── crawler.py       # 爬虫核心
│   ├── downloader.py    # 下载模块
│   └── config.py        # 配置管理
└── downloads/           # 下载目录
```

## 注意事项

- 请遵守网站的使用条款和 robots.txt
- 合理设置请求间隔，避免对服务器造成过大压力
- 定期更新 cookie 以保持登录状态
