#!/usr/bin/env python3
"""
安装脚本 - 自动安装依赖和设置环境
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.8或更高版本")
        return False


def install_dependencies():
    """安装依赖包"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False
    
    # 升级pip
    run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip")
    
    # 安装依赖
    return run_command(f"{sys.executable} -m pip install -r requirements.txt", "安装依赖包")


def setup_directories():
    """创建必要的目录"""
    print("📁 创建目录结构...")
    
    directories = ["downloads", "logs"]
    for dir_name in directories:
        dir_path = Path(dir_name)
        dir_path.mkdir(exist_ok=True)
        print(f"✅ 目录已创建: {dir_path}")
    
    return True


def check_config():
    """检查配置文件"""
    print("⚙️  检查配置文件...")
    
    config_file = Path("config.json")
    if config_file.exists():
        print("✅ config.json 已存在")
        
        # 检查是否需要更新cookies
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if not config.get('cookies'):
                print("⚠️  配置文件中未设置cookies")
                print("   请编辑 config.json 文件，添加您的cookies")
                return False
            else:
                print("✅ cookies 已配置")
                return True
                
        except Exception as e:
            print(f"❌ 配置文件格式错误: {e}")
            return False
    else:
        print("❌ config.json 不存在")
        return False


def run_test():
    """运行测试"""
    print("🧪 运行测试...")
    return run_command(f"{sys.executable} test_crawler.py", "运行功能测试")


def main():
    """主安装函数"""
    print("🚀 Ideogram AI 爬虫安装程序")
    print("=" * 50)
    
    steps = [
        ("检查Python版本", check_python_version),
        ("安装依赖包", install_dependencies),
        ("创建目录结构", setup_directories),
        ("检查配置文件", check_config),
    ]
    
    # 执行安装步骤
    success_count = 0
    for step_name, step_func in steps:
        if step_func():
            success_count += 1
        else:
            print(f"\n❌ 安装步骤 '{step_name}' 失败")
            break
    
    print("\n" + "=" * 50)
    
    if success_count == len(steps):
        print("🎉 安装完成！")
        
        # 运行测试
        print("\n🧪 运行测试验证...")
        if run_test():
            print("\n✅ 所有测试通过！")
        else:
            print("\n⚠️  测试未完全通过，请检查配置")
        
        print("\n💡 使用方法:")
        print("   python main.py --help           # 查看帮助")
        print("   python main.py --test-only      # 测试连接")
        print("   python main.py --limit 10       # 下载10张图片")
        print("   python test_crawler.py          # 运行测试")
        
    else:
        print("❌ 安装失败")
        print("\n🔧 故障排除:")
        print("   1. 确保网络连接正常")
        print("   2. 检查Python版本是否符合要求")
        print("   3. 尝试手动安装依赖: pip install -r requirements.txt")
        print("   4. 检查config.json文件是否正确配置")


if __name__ == '__main__':
    main()
