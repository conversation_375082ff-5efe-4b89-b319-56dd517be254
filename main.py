#!/usr/bin/env python3
"""
Ideogram AI 图片爬虫主程序
"""

import asyncio
import logging
import sys
from pathlib import Path
import click
from typing import Optional

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.config import ConfigManager
from src.crawler import IdeogramCrawler
from src.downloader import ImageDownloader


def setup_logging(verbose: bool = False):
    """设置日志配置"""
    level = logging.DEBUG if verbose else logging.INFO
    
    # 创建日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(level)
    
    # 文件处理器
    file_handler = logging.FileHandler('ideogram_crawler.log', encoding='utf-8')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.DEBUG)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # 减少第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)


@click.command()
@click.option('--config', '-c', default='config.json', help='配置文件路径')
@click.option('--limit', '-l', type=int, help='限制下载图片数量')
@click.option('--keyword', '-k', help='搜索关键词（暂未实现）')
@click.option('--output', '-o', help='输出目录')
@click.option('--verbose', '-v', is_flag=True, help='详细输出')
@click.option('--test-only', is_flag=True, help='仅测试连接，不下载')
@click.option('--cleanup', is_flag=True, help='清理无效的下载文件')
def main(config: str, limit: Optional[int], keyword: Optional[str], 
         output: Optional[str], verbose: bool, test_only: bool, cleanup: bool):
    """
    Ideogram AI 图片爬虫
    
    使用提供的 cookie 登录 Ideogram AI 并下载角色页面的图片。
    """
    # 设置日志
    setup_logging(verbose)
    logger = logging.getLogger(__name__)
    
    try:
        # 加载配置
        logger.info("加载配置文件...")
        config_manager = ConfigManager(config)
        
        # 如果指定了输出目录，更新配置
        if output:
            config_manager.set('download_dir', output)
            config_manager.ensure_download_dir()
        
        # 创建爬虫实例
        logger.info("初始化爬虫...")
        crawler = IdeogramCrawler(config_manager)
        
        # 测试连接
        logger.info("测试连接和认证...")
        if not crawler.test_connection():
            logger.error("连接测试失败，请检查网络连接和 cookie 配置")
            sys.exit(1)
        
        logger.info("连接测试成功！")
        
        if test_only:
            logger.info("仅测试模式，程序结束")
            return
        
        # 创建下载器
        downloader = ImageDownloader(config_manager)
        
        # 清理无效文件
        if cleanup:
            logger.info("清理无效的下载文件...")
            downloader.cleanup_failed_downloads()
        
        # 开始爬取
        logger.info("开始爬取图片信息...")
        images = list(crawler.crawl_character_images(limit=limit))
        
        if not images:
            logger.warning("未找到任何图片")
            return
        
        logger.info(f"找到 {len(images)} 张图片，开始下载...")
        
        # 异步下载图片
        results = asyncio.run(downloader.download_images(images))
        
        # 显示统计信息
        stats = downloader.get_download_stats()
        logger.info("下载完成！")
        logger.info(f"总计: {stats['total']}")
        logger.info(f"成功: {stats['success']}")
        logger.info(f"失败: {stats['failed']}")
        logger.info(f"跳过: {stats['skipped']}")
        
        # 显示失败的下载
        failed_downloads = [r for r in results if not r['success'] and not r.get('skipped')]
        if failed_downloads:
            logger.warning(f"以下 {len(failed_downloads)} 个文件下载失败:")
            for result in failed_downloads:
                logger.warning(f"  {result['filename']}: {result['error']}")
        
        # 显示下载目录
        download_dir = config_manager.get_download_dir()
        logger.info(f"文件保存在: {download_dir.absolute()}")
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行出错: {e}", exc_info=verbose)
        sys.exit(1)


@click.group()
def cli():
    """Ideogram AI 爬虫工具集"""
    pass


@cli.command()
@click.option('--config', '-c', default='config.json', help='配置文件路径')
def test_auth(config: str):
    """测试认证状态"""
    setup_logging(True)
    logger = logging.getLogger(__name__)
    
    try:
        config_manager = ConfigManager(config)
        crawler = IdeogramCrawler(config_manager)
        
        logger.info("测试认证状态...")
        if crawler.test_connection():
            logger.info("✅ 认证成功")
            
            # 尝试获取用户信息
            user_info = crawler.auth.get_user_info()
            if user_info:
                logger.info(f"用户信息: {user_info}")
        else:
            logger.error("❌ 认证失败")
            
    except Exception as e:
        logger.error(f"测试失败: {e}")


@cli.command()
@click.option('--config', '-c', default='config.json', help='配置文件路径')
@click.argument('cookies')
def update_cookies(config: str, cookies: str):
    """更新配置文件中的 cookies"""
    try:
        config_manager = ConfigManager(config)
        config_manager.update_cookies(cookies)
        print("✅ Cookies 更新成功")
    except Exception as e:
        print(f"❌ 更新失败: {e}")


if __name__ == '__main__':
    # 如果直接运行，使用main命令
    if len(sys.argv) == 1:
        main()
    else:
        cli()
