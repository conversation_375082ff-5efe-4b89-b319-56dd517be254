#!/usr/bin/env python3
"""
测试脚本 - 验证爬虫功能
"""

import sys
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.config import ConfigManager
from src.crawler import IdeogramCrawler
from src.auth import IdeogramAuth


def test_config():
    """测试配置管理"""
    print("🧪 测试配置管理...")
    
    try:
        config = ConfigManager('config.json')
        print(f"✅ 配置加载成功")
        print(f"   基础URL: {config.get('base_url')}")
        print(f"   角色URL: {config.get('character_url')}")
        print(f"   下载目录: {config.get('download_dir')}")
        print(f"   最大并发: {config.get('max_concurrent_downloads')}")
        
        # 测试下载目录创建
        config.ensure_download_dir()
        download_dir = config.get_download_dir()
        if download_dir.exists():
            print(f"✅ 下载目录已创建: {download_dir}")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_auth():
    """测试认证功能"""
    print("\n🔐 测试认证功能...")
    
    try:
        config = ConfigManager('config.json')
        auth = IdeogramAuth(config.get_session_config())
        
        # 检查cookies是否设置
        cookies = config.get('cookies')
        if cookies:
            print(f"✅ Cookies 已配置 (长度: {len(cookies)} 字符)")
        else:
            print("⚠️  未配置 Cookies")
            return False
        
        # 测试认证
        if auth.test_authentication():
            print("✅ 认证测试成功")
            
            # 尝试获取用户信息
            user_info = auth.get_user_info()
            if user_info:
                print(f"✅ 获取到用户信息: {type(user_info)}")
            else:
                print("⚠️  无法获取用户信息（可能是API端点问题）")
            
            return True
        else:
            print("❌ 认证测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 认证测试出错: {e}")
        return False


def test_crawler():
    """测试爬虫功能"""
    print("\n🕷️  测试爬虫功能...")
    
    try:
        config = ConfigManager('config.json')
        crawler = IdeogramCrawler(config)
        
        # 测试连接
        if not crawler.test_connection():
            print("❌ 爬虫连接测试失败")
            return False
        
        print("✅ 爬虫连接测试成功")
        
        # 获取页面内容
        print("📄 获取页面内容...")
        html_content = crawler.get_page_content(crawler.character_url)
        
        if html_content:
            print(f"✅ 页面内容获取成功 (长度: {len(html_content)} 字符)")
            
            # 解析页面
            print("🔍 解析页面内容...")
            images = crawler.parse_character_page(html_content)
            print(f"✅ 解析完成，找到 {len(images)} 张图片")
            
            # 显示前几张图片信息
            for i, img in enumerate(images[:3]):
                print(f"   图片 {i+1}: {img.get('filename', 'unknown')} - {img.get('url', 'no url')[:50]}...")
            
            return len(images) > 0
        else:
            print("❌ 无法获取页面内容")
            return False
            
    except Exception as e:
        print(f"❌ 爬虫测试出错: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试 Ideogram AI 爬虫")
    print("=" * 50)
    
    # 设置简单的日志
    logging.basicConfig(level=logging.WARNING)
    
    # 运行测试
    tests = [
        ("配置管理", test_config),
        ("认证功能", test_auth),
        ("爬虫功能", test_crawler),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！爬虫已准备就绪。")
        print("\n💡 使用方法:")
        print("   python main.py --test-only  # 仅测试连接")
        print("   python main.py --limit 10   # 下载10张图片")
        print("   python main.py --verbose    # 详细输出")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")
        
        if not results[1][1]:  # 认证测试失败
            print("\n🔧 认证问题排查:")
            print("   1. 检查 config.json 中的 cookies 是否正确")
            print("   2. 确认 cookies 是否已过期")
            print("   3. 尝试重新登录获取新的 cookies")


if __name__ == '__main__':
    main()
