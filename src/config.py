"""
配置管理模块
处理配置文件的加载、验证和管理
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    DEFAULT_CONFIG = {
        "base_url": "https://ideogram.ai",
        "character_url": "https://ideogram.ai/character",
        "download_dir": "./downloads",
        "max_concurrent_downloads": 5,
        "request_delay": 1,
        "timeout": 30,
        "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "headers": {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none"
        },
        "cookies": ""
    }
    
    def __init__(self, config_path: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config = self.DEFAULT_CONFIG.copy()
        self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            Dict: 配置字典
        """
        if not self.config_path.exists():
            logger.warning(f"配置文件 {self.config_path} 不存在，使用默认配置")
            self.save_config()  # 创建默认配置文件
            return self.config
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            
            # 合并配置，文件配置覆盖默认配置
            self.config.update(file_config)
            
            # 验证配置
            self._validate_config()
            
            logger.info(f"配置文件 {self.config_path} 加载成功")
            return self.config
            
        except json.JSONDecodeError as e:
            logger.error(f"配置文件 JSON 格式错误: {e}")
            raise
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            logger.info(f"配置已保存到 {self.config_path}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            raise
    
    def _validate_config(self):
        """验证配置的有效性"""
        required_fields = ['base_url', 'character_url', 'download_dir']
        
        for field in required_fields:
            if field not in self.config or not self.config[field]:
                raise ValueError(f"配置项 '{field}' 是必需的")
        
        # 验证数值类型
        numeric_fields = {
            'max_concurrent_downloads': int,
            'request_delay': (int, float),
            'timeout': (int, float)
        }
        
        for field, expected_type in numeric_fields.items():
            if field in self.config:
                if not isinstance(self.config[field], expected_type):
                    try:
                        if expected_type == int:
                            self.config[field] = int(self.config[field])
                        elif expected_type == (int, float):
                            self.config[field] = float(self.config[field])
                    except (ValueError, TypeError):
                        raise ValueError(f"配置项 '{field}' 必须是数值类型")
        
        # 验证下载目录
        download_dir = Path(self.config['download_dir'])
        if not download_dir.exists():
            try:
                download_dir.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建下载目录: {download_dir}")
            except Exception as e:
                logger.error(f"无法创建下载目录 {download_dir}: {e}")
                raise
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any):
        """
        设置配置项
        
        Args:
            key: 配置键
            value: 配置值
        """
        self.config[key] = value
    
    def update_cookies(self, cookies: str):
        """
        更新 cookies 配置
        
        Args:
            cookies: 新的 cookie 字符串
        """
        self.config['cookies'] = cookies
        self.save_config()
        logger.info("Cookies 配置已更新")
    
    def get_download_dir(self) -> Path:
        """
        获取下载目录路径对象
        
        Returns:
            Path: 下载目录路径
        """
        return Path(self.config['download_dir'])
    
    def ensure_download_dir(self):
        """确保下载目录存在"""
        download_dir = self.get_download_dir()
        download_dir.mkdir(parents=True, exist_ok=True)
    
    def get_session_config(self) -> Dict[str, Any]:
        """
        获取会话相关配置
        
        Returns:
            Dict: 会话配置
        """
        return {
            'cookies': self.get('cookies'),
            'headers': self.get('headers'),
            'user_agent': self.get('user_agent'),
            'timeout': self.get('timeout'),
            'base_url': self.get('base_url'),
            'character_url': self.get('character_url')
        }
    
    def __getitem__(self, key: str) -> Any:
        """支持字典式访问"""
        return self.config[key]
    
    def __setitem__(self, key: str, value: Any):
        """支持字典式设置"""
        self.config[key] = value
    
    def __contains__(self, key: str) -> bool:
        """支持 in 操作符"""
        return key in self.config
