"""
Ideogram AI 爬虫核心模块
处理网页爬取、数据解析和图片信息提取
"""

import re
import json
import time
import logging
from typing import List, Dict, Any, Optional, Generator
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from .auth import IdeogramAuth
from .config import ConfigManager

logger = logging.getLogger(__name__)


class IdeogramCrawler:
    """Ideogram AI 爬虫核心类"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化爬虫
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.auth = IdeogramAuth(config_manager.get_session_config())
        self.session = self.auth.get_authenticated_session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        self.base_url = config_manager.get('base_url')
        self.character_url = config_manager.get('character_url')
        self.request_delay = config_manager.get('request_delay', 1)
        
    def test_connection(self) -> bool:
        """
        测试连接和认证状态
        
        Returns:
            bool: 连接是否成功
        """
        logger.info("测试连接和认证状态...")
        return self.auth.test_authentication()
    
    def get_page_content(self, url: str) -> Optional[str]:
        """
        获取页面内容
        
        Args:
            url: 页面URL
            
        Returns:
            str: 页面HTML内容，失败返回None
        """
        try:
            response = self.session.get(
                url,
                timeout=self.config.get('timeout', 30)
            )
            response.raise_for_status()
            
            # 添加请求延迟
            time.sleep(self.request_delay)
            
            return response.text
            
        except requests.RequestException as e:
            logger.error(f"获取页面内容失败 {url}: {e}")
            return None
    
    def parse_character_page(self, html_content: str) -> List[Dict[str, Any]]:
        """
        解析角色页面，提取图片信息
        
        Args:
            html_content: 页面HTML内容
            
        Returns:
            List[Dict]: 图片信息列表
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        images = []
        
        # 查找图片容器 - 根据实际页面结构调整选择器
        image_containers = soup.find_all(['img', 'div'], class_=re.compile(r'image|photo|picture|gallery'))
        
        for container in image_containers:
            image_info = self._extract_image_info(container)
            if image_info:
                images.append(image_info)
        
        # 尝试从 script 标签中提取 JSON 数据
        script_images = self._extract_images_from_scripts(soup)
        images.extend(script_images)
        
        logger.info(f"从页面中提取到 {len(images)} 张图片信息")
        return images
    
    def _extract_image_info(self, element) -> Optional[Dict[str, Any]]:
        """
        从HTML元素中提取图片信息
        
        Args:
            element: BeautifulSoup元素
            
        Returns:
            Dict: 图片信息字典
        """
        image_info = {}
        
        # 提取图片URL
        img_url = None
        if element.name == 'img':
            img_url = element.get('src') or element.get('data-src')
        else:
            # 在容器中查找img标签
            img_tag = element.find('img')
            if img_tag:
                img_url = img_tag.get('src') or img_tag.get('data-src')
        
        if not img_url:
            return None
        
        # 处理相对URL
        if img_url.startswith('//'):
            img_url = 'https:' + img_url
        elif img_url.startswith('/'):
            img_url = urljoin(self.base_url, img_url)
        
        image_info['url'] = img_url
        image_info['filename'] = self._generate_filename(img_url)
        
        # 提取其他信息
        image_info['title'] = element.get('alt') or element.get('title', '')
        image_info['width'] = element.get('width')
        image_info['height'] = element.get('height')
        
        return image_info
    
    def _extract_images_from_scripts(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        从页面的JavaScript代码中提取图片信息
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            List[Dict]: 图片信息列表
        """
        images = []
        script_tags = soup.find_all('script', type='application/json')
        
        for script in script_tags:
            try:
                data = json.loads(script.string or '{}')
                script_images = self._parse_json_for_images(data)
                images.extend(script_images)
            except (json.JSONDecodeError, AttributeError):
                continue
        
        # 也检查普通的script标签中的JSON数据
        for script in soup.find_all('script'):
            if script.string:
                # 查找JSON格式的图片数据
                json_matches = re.findall(r'\{[^{}]*"url"[^{}]*\}', script.string)
                for match in json_matches:
                    try:
                        data = json.loads(match)
                        if 'url' in data:
                            image_info = {
                                'url': data['url'],
                                'filename': self._generate_filename(data['url']),
                                'title': data.get('title', ''),
                                'width': data.get('width'),
                                'height': data.get('height')
                            }
                            images.append(image_info)
                    except json.JSONDecodeError:
                        continue
        
        return images
    
    def _parse_json_for_images(self, data: Any) -> List[Dict[str, Any]]:
        """
        递归解析JSON数据中的图片信息
        
        Args:
            data: JSON数据
            
        Returns:
            List[Dict]: 图片信息列表
        """
        images = []
        
        if isinstance(data, dict):
            # 检查是否包含图片URL
            if 'url' in data and self._is_image_url(data['url']):
                image_info = {
                    'url': data['url'],
                    'filename': self._generate_filename(data['url']),
                    'title': data.get('title', ''),
                    'width': data.get('width'),
                    'height': data.get('height')
                }
                images.append(image_info)
            
            # 递归检查所有值
            for value in data.values():
                images.extend(self._parse_json_for_images(value))
                
        elif isinstance(data, list):
            for item in data:
                images.extend(self._parse_json_for_images(item))
        
        return images
    
    def _is_image_url(self, url: str) -> bool:
        """
        检查URL是否为图片URL
        
        Args:
            url: URL字符串
            
        Returns:
            bool: 是否为图片URL
        """
        if not isinstance(url, str):
            return False
        
        # 检查文件扩展名
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'}
        parsed_url = urlparse(url.lower())
        path = parsed_url.path
        
        return any(path.endswith(ext) for ext in image_extensions)
    
    def _generate_filename(self, url: str) -> str:
        """
        从URL生成文件名
        
        Args:
            url: 图片URL
            
        Returns:
            str: 生成的文件名
        """
        parsed_url = urlparse(url)
        filename = parsed_url.path.split('/')[-1]
        
        if not filename or '.' not in filename:
            # 如果没有文件名或扩展名，生成一个
            import hashlib
            url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
            filename = f"image_{url_hash}.jpg"
        
        return filename
    
    def crawl_character_images(self, limit: Optional[int] = None) -> Generator[Dict[str, Any], None, None]:
        """
        爬取角色页面的图片
        
        Args:
            limit: 限制爬取数量
            
        Yields:
            Dict: 图片信息
        """
        logger.info(f"开始爬取角色页面图片，限制数量: {limit or '无限制'}")
        
        html_content = self.get_page_content(self.character_url)
        if not html_content:
            logger.error("无法获取角色页面内容")
            return
        
        images = self.parse_character_page(html_content)
        
        count = 0
        for image in images:
            if limit and count >= limit:
                break
            
            yield image
            count += 1
        
        logger.info(f"爬取完成，共处理 {count} 张图片")
