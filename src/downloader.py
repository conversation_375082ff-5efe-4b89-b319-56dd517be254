"""
图片下载模块
支持异步批量下载和文件管理
"""

import os
import asyncio
import aiohttp
import aiofiles
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
from tqdm.asyncio import tqdm
import hashlib
import time

from .config import ConfigManager

logger = logging.getLogger(__name__)


class ImageDownloader:
    """图片下载器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化下载器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.download_dir = config_manager.get_download_dir()
        self.max_concurrent = config_manager.get('max_concurrent_downloads', 5)
        self.timeout = config_manager.get('timeout', 30)
        self.session_config = config_manager.get_session_config()
        
        # 确保下载目录存在
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        # 下载统计
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
    
    async def download_image(self, session: aiohttp.ClientSession, image_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        下载单张图片
        
        Args:
            session: aiohttp会话
            image_info: 图片信息字典
            
        Returns:
            Dict: 下载结果
        """
        url = image_info['url']
        filename = image_info['filename']
        filepath = self.download_dir / filename
        
        result = {
            'url': url,
            'filename': filename,
            'filepath': str(filepath),
            'success': False,
            'error': None,
            'size': 0
        }
        
        try:
            # 检查文件是否已存在
            if filepath.exists():
                result['success'] = True
                result['size'] = filepath.stat().st_size
                result['skipped'] = True
                self.stats['skipped'] += 1
                logger.debug(f"文件已存在，跳过: {filename}")
                return result
            
            # 下载图片
            async with session.get(url) as response:
                if response.status == 200:
                    content = await response.read()
                    
                    # 验证内容是否为图片
                    if not self._is_valid_image_content(content):
                        raise ValueError("下载的内容不是有效的图片")
                    
                    # 保存文件
                    async with aiofiles.open(filepath, 'wb') as f:
                        await f.write(content)
                    
                    result['success'] = True
                    result['size'] = len(content)
                    self.stats['success'] += 1
                    logger.debug(f"下载成功: {filename} ({len(content)} bytes)")
                    
                else:
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status
                    )
        
        except Exception as e:
            result['error'] = str(e)
            self.stats['failed'] += 1
            logger.error(f"下载失败 {filename}: {e}")
            
            # 删除可能的不完整文件
            if filepath.exists():
                try:
                    filepath.unlink()
                except Exception:
                    pass
        
        return result
    
    def _is_valid_image_content(self, content: bytes) -> bool:
        """
        验证内容是否为有效的图片
        
        Args:
            content: 文件内容
            
        Returns:
            bool: 是否为有效图片
        """
        if len(content) < 10:
            return False
        
        # 检查常见图片格式的文件头
        image_signatures = [
            b'\xFF\xD8\xFF',  # JPEG
            b'\x89PNG\r\n\x1a\n',  # PNG
            b'GIF87a',  # GIF87a
            b'GIF89a',  # GIF89a
            b'RIFF',  # WebP (需要进一步检查)
            b'BM',  # BMP
        ]
        
        for signature in image_signatures:
            if content.startswith(signature):
                return True
        
        # WebP 额外检查
        if content.startswith(b'RIFF') and b'WEBP' in content[:12]:
            return True
        
        return False
    
    async def download_images(self, images: List[Dict[str, Any]], show_progress: bool = True) -> List[Dict[str, Any]]:
        """
        批量下载图片
        
        Args:
            images: 图片信息列表
            show_progress: 是否显示进度条
            
        Returns:
            List[Dict]: 下载结果列表
        """
        if not images:
            logger.warning("没有图片需要下载")
            return []
        
        self.stats = {'total': len(images), 'success': 0, 'failed': 0, 'skipped': 0}
        
        # 创建aiohttp会话
        connector = aiohttp.TCPConnector(limit=self.max_concurrent)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        headers = self.session_config.get('headers', {}).copy()
        headers['User-Agent'] = self.session_config.get('user_agent', '')
        
        # 处理cookies
        cookies = {}
        cookie_string = self.session_config.get('cookies', '')
        if cookie_string:
            for cookie in cookie_string.split(';'):
                cookie = cookie.strip()
                if '=' in cookie:
                    name, value = cookie.split('=', 1)
                    cookies[name.strip()] = value.strip()
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=headers,
            cookies=cookies
        ) as session:
            
            # 创建下载任务
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def download_with_semaphore(image_info):
                async with semaphore:
                    return await self.download_image(session, image_info)
            
            tasks = [download_with_semaphore(img) for img in images]
            
            # 执行下载任务
            if show_progress:
                results = await tqdm.gather(*tasks, desc="下载图片")
            else:
                results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        'url': images[i]['url'],
                        'filename': images[i]['filename'],
                        'success': False,
                        'error': str(result),
                        'size': 0
                    })
                    self.stats['failed'] += 1
                else:
                    processed_results.append(result)
        
        return processed_results
    
    def get_download_stats(self) -> Dict[str, Any]:
        """
        获取下载统计信息
        
        Returns:
            Dict: 统计信息
        """
        return self.stats.copy()
    
    def cleanup_failed_downloads(self):
        """清理失败的下载文件"""
        cleaned = 0
        for file_path in self.download_dir.glob('*'):
            if file_path.is_file():
                try:
                    # 检查文件大小，删除空文件或过小的文件
                    if file_path.stat().st_size < 1024:  # 小于1KB
                        with open(file_path, 'rb') as f:
                            content = f.read()
                        
                        if not self._is_valid_image_content(content):
                            file_path.unlink()
                            cleaned += 1
                            logger.debug(f"清理无效文件: {file_path.name}")
                
                except Exception as e:
                    logger.error(f"清理文件失败 {file_path}: {e}")
        
        if cleaned > 0:
            logger.info(f"清理了 {cleaned} 个无效文件")
    
    def generate_unique_filename(self, original_filename: str) -> str:
        """
        生成唯一的文件名
        
        Args:
            original_filename: 原始文件名
            
        Returns:
            str: 唯一文件名
        """
        filepath = self.download_dir / original_filename
        
        if not filepath.exists():
            return original_filename
        
        # 文件已存在，生成新的文件名
        name, ext = os.path.splitext(original_filename)
        counter = 1
        
        while True:
            new_filename = f"{name}_{counter}{ext}"
            new_filepath = self.download_dir / new_filename
            
            if not new_filepath.exists():
                return new_filename
            
            counter += 1
            
            # 防止无限循环
            if counter > 1000:
                # 使用时间戳
                timestamp = int(time.time())
                return f"{name}_{timestamp}{ext}"
