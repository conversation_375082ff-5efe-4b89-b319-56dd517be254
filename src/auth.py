"""
Ideogram AI 认证模块
处理 Cookie 认证和会话管理
"""

import requests
import json
import logging
from typing import Dict, Optional, Any
from urllib.parse import urljoin

logger = logging.getLogger(__name__)


class IdeogramAuth:
    """Ideogram AI 认证类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化认证模块
        
        Args:
            config: 配置字典，包含 cookies、headers 等信息
        """
        self.config = config
        self.session = requests.Session()
        self.base_url = config.get('base_url', 'https://ideogram.ai')
        self.character_url = config.get('character_url', 'https://ideogram.ai/character')
        
        # 设置基础 headers
        self.session.headers.update(config.get('headers', {}))
        self.session.headers.update({
            'User-Agent': config.get('user_agent', 
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36')
        })
        
        # 解析并设置 cookies
        self._setup_cookies()
        
    def _setup_cookies(self):
        """解析并设置 cookies"""
        cookie_string = self.config.get('cookies', '')
        if not cookie_string:
            logger.warning("未提供 cookies，可能无法访问需要认证的内容")
            return
            
        # 解析 cookie 字符串
        cookies = {}
        for cookie in cookie_string.split(';'):
            cookie = cookie.strip()
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                cookies[name.strip()] = value.strip()
        
        # 设置到 session
        for name, value in cookies.items():
            self.session.cookies.set(name, value)
            
        logger.info(f"已设置 {len(cookies)} 个 cookies")
    
    def test_authentication(self) -> bool:
        """
        测试认证是否有效
        
        Returns:
            bool: 认证是否成功
        """
        try:
            response = self.session.get(
                self.character_url,
                timeout=self.config.get('timeout', 30)
            )
            
            if response.status_code == 200:
                # 检查是否包含登录用户的标识
                content = response.text.lower()
                
                # 检查是否重定向到登录页面或包含登录相关内容
                if 'login' in content or 'sign in' in content:
                    logger.warning("可能需要重新登录，检测到登录相关内容")
                    return False
                    
                # 检查是否包含用户相关内容
                if any(keyword in content for keyword in ['user', 'profile', 'dashboard', 'generate']):
                    logger.info("认证测试成功")
                    return True
                    
                logger.warning("无法确定认证状态")
                return True  # 假设成功，让后续流程处理
                
            else:
                logger.error(f"认证测试失败，状态码: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            logger.error(f"认证测试出错: {e}")
            return False
    
    def get_authenticated_session(self) -> requests.Session:
        """
        获取已认证的会话对象
        
        Returns:
            requests.Session: 已配置认证信息的会话对象
        """
        return self.session
    
    def refresh_cookies(self, new_cookies: str):
        """
        刷新 cookies
        
        Args:
            new_cookies: 新的 cookie 字符串
        """
        self.config['cookies'] = new_cookies
        self.session.cookies.clear()
        self._setup_cookies()
        logger.info("Cookies 已刷新")
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        获取用户信息
        
        Returns:
            Dict: 用户信息字典，如果获取失败返回 None
        """
        try:
            response = self.session.get(
                urljoin(self.base_url, '/api/user'),
                timeout=self.config.get('timeout', 30)
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"获取用户信息失败，状态码: {response.status_code}")
                return None
                
        except requests.RequestException as e:
            logger.error(f"获取用户信息出错: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"解析用户信息 JSON 失败: {e}")
            return None
    
    def is_session_valid(self) -> bool:
        """
        检查会话是否仍然有效
        
        Returns:
            bool: 会话是否有效
        """
        return self.test_authentication()
