#!/usr/bin/env python3
"""
API 接口分析工具
帮助分析 Ideogram AI 的实际数据获取接口
"""

import sys
import requests
import json
import re
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.config import ConfigManager
from src.auth import IdeogramAuth


def analyze_upload_request():
    """分析上传请求的结构"""
    print("📋 上传接口分析:")
    print("=" * 50)
    
    upload_info = {
        "url": "https://ideogram.ai/api/reference-collections/createReferenceCollection",
        "method": "POST",
        "content_type": "multipart/form-data",
        "purpose": "创建角色引用集合（上传图片）",
        "auth_type": "Bearer Token",
        "data_fields": {
            "name": "集合名称",
            "reference_type": "引用类型（CHARACTER）",
            "asset_blobs": "图片文件数据"
        }
    }
    
    for key, value in upload_info.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for k, v in value.items():
                print(f"  {k}: {v}")
        else:
            print(f"{key}: {value}")
    
    print("\n⚠️  结论: 这是上传接口，不适合爬虫使用")


def suggest_api_endpoints():
    """建议可能的数据获取接口"""
    print("\n🔍 建议的数据获取接口:")
    print("=" * 50)
    
    possible_endpoints = [
        {
            "url": "https://ideogram.ai/api/reference-collections/list",
            "purpose": "获取引用集合列表",
            "method": "GET"
        },
        {
            "url": "https://ideogram.ai/api/images/list",
            "purpose": "获取图片列表",
            "method": "GET"
        },
        {
            "url": "https://ideogram.ai/api/images/search",
            "purpose": "搜索图片",
            "method": "GET/POST"
        },
        {
            "url": "https://ideogram.ai/api/gallery",
            "purpose": "获取画廊内容",
            "method": "GET"
        },
        {
            "url": "https://ideogram.ai/api/character/images",
            "purpose": "获取角色相关图片",
            "method": "GET"
        }
    ]
    
    for i, endpoint in enumerate(possible_endpoints, 1):
        print(f"{i}. {endpoint['url']}")
        print(f"   目的: {endpoint['purpose']}")
        print(f"   方法: {endpoint['method']}")
        print()


def test_api_endpoints():
    """测试可能的API接口"""
    print("🧪 测试API接口:")
    print("=" * 50)
    
    try:
        config = ConfigManager('config.json')
        auth = IdeogramAuth(config.get_session_config())
        session = auth.get_authenticated_session()
        
        # 从session cookie中提取Bearer token
        bearer_token = extract_bearer_token(session)
        if bearer_token:
            session.headers['Authorization'] = f'Bearer {bearer_token}'
        
        base_url = "https://ideogram.ai/api"
        test_endpoints = [
            "/reference-collections",
            "/images",
            "/gallery",
            "/character",
            "/user/images",
            "/explore"
        ]
        
        for endpoint in test_endpoints:
            url = base_url + endpoint
            print(f"测试: {url}")
            
            try:
                response = session.get(url, timeout=10)
                print(f"  状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"  响应类型: JSON")
                        print(f"  数据键: {list(data.keys()) if isinstance(data, dict) else 'Array'}")
                    except:
                        print(f"  响应类型: 文本 ({len(response.text)} 字符)")
                elif response.status_code == 404:
                    print(f"  结果: 接口不存在")
                elif response.status_code == 401:
                    print(f"  结果: 需要认证")
                elif response.status_code == 403:
                    print(f"  结果: 权限不足")
                else:
                    print(f"  结果: 其他错误")
                    
            except requests.RequestException as e:
                print(f"  错误: {e}")
            
            print()
            
    except Exception as e:
        print(f"测试失败: {e}")


def extract_bearer_token(session):
    """从session中提取Bearer token"""
    # 尝试从session cookie中提取token
    for cookie in session.cookies:
        if 'session' in cookie.name.lower():
            # 这里需要根据实际的token格式进行解析
            # 通常JWT token在session cookie中
            return cookie.value
    return None


def analyze_page_network():
    """分析页面网络请求的建议"""
    print("🌐 页面网络分析建议:")
    print("=" * 50)
    
    suggestions = [
        "1. 打开浏览器开发者工具 (F12)",
        "2. 访问 https://ideogram.ai/character",
        "3. 在 Network 标签页中查看所有请求",
        "4. 寻找返回图片数据的 API 请求",
        "5. 特别关注以下类型的请求:",
        "   - XHR/Fetch 请求",
        "   - 返回 JSON 数据的请求",
        "   - URL 包含 'api' 的请求",
        "6. 复制有用的请求信息用于爬虫开发"
    ]
    
    for suggestion in suggestions:
        print(suggestion)


def main():
    """主函数"""
    print("🔍 Ideogram AI API 接口分析工具")
    print("=" * 60)
    
    # 分析上传请求
    analyze_upload_request()
    
    # 建议可能的接口
    suggest_api_endpoints()
    
    # 测试接口
    test_api_endpoints()
    
    # 分析建议
    analyze_page_network()
    
    print("\n" + "=" * 60)
    print("📝 总结:")
    print("1. upload.md 中的接口是上传接口，不适合爬虫")
    print("2. 需要找到获取现有图片数据的接口")
    print("3. 建议使用浏览器开发者工具分析实际的数据接口")
    print("4. 可能需要Bearer Token认证而不是Cookie认证")


if __name__ == '__main__':
    main()
