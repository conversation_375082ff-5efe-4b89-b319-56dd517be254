#!/usr/bin/env python3
"""
简单的登录测试脚本
验证是否可以使用 cookie 成功登录 Ideogram AI
"""

import sys
import requests
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.config import Config<PERSON><PERSON><PERSON>


def test_login():
    """测试登录状态"""
    print("🔐 测试 Ideogram AI 登录状态...")
    print("=" * 50)
    
    try:
        # 加载配置
        config = ConfigManager('config.json')
        
        # 获取配置信息
        cookies_str = config.get('cookies')
        character_url = config.get('character_url')
        headers = config.get('headers', {})
        user_agent = config.get('user_agent')
        
        if not cookies_str:
            print("❌ 未配置 cookies")
            return False
        
        print(f"📍 目标URL: {character_url}")
        print(f"🍪 Cookies长度: {len(cookies_str)} 字符")
        
        # 创建会话
        session = requests.Session()
        
        # 设置 headers
        session.headers.update(headers)
        session.headers['User-Agent'] = user_agent
        
        # 解析并设置 cookies
        cookies = {}
        for cookie in cookies_str.split(';'):
            cookie = cookie.strip()
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                cookies[name.strip()] = value.strip()
        
        for name, value in cookies.items():
            session.cookies.set(name, value)
        
        print(f"🍪 已设置 {len(cookies)} 个 cookies")
        
        # 发送请求
        print("🌐 发送请求...")
        response = session.get(character_url, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📏 响应内容长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            content = response.text.lower()
            
            # 检查登录状态的关键词
            login_indicators = ['login', 'sign in', 'log in', 'signin']
            logout_indicators = ['logout', 'sign out', 'profile', 'dashboard', 'user', 'account']
            
            has_login = any(indicator in content for indicator in login_indicators)
            has_logout = any(indicator in content for indicator in logout_indicators)
            
            print("\n🔍 页面内容分析:")
            print(f"   包含登录相关内容: {'是' if has_login else '否'}")
            print(f"   包含用户相关内容: {'是' if has_logout else '否'}")
            
            # 显示页面标题
            import re
            title_match = re.search(r'<title[^>]*>(.*?)</title>', response.text, re.IGNORECASE)
            if title_match:
                title = title_match.group(1).strip()
                print(f"   页面标题: {title}")
            
            # 检查是否重定向到登录页面
            if 'login' in response.url.lower():
                print("❌ 页面重定向到登录页面，认证失败")
                return False
            
            # 判断登录状态
            if has_login and not has_logout:
                print("❌ 页面显示需要登录，认证失败")
                return False
            elif has_logout or not has_login:
                print("✅ 登录状态正常")
                return True
            else:
                print("⚠️  无法确定登录状态，但页面访问成功")
                return True
        
        elif response.status_code == 302 or response.status_code == 301:
            print(f"🔄 页面重定向到: {response.headers.get('Location', '未知')}")
            if 'login' in response.headers.get('Location', '').lower():
                print("❌ 重定向到登录页面，认证失败")
                return False
            else:
                print("✅ 重定向但非登录页面，可能正常")
                return True
        
        elif response.status_code == 403:
            print("❌ 访问被拒绝，可能是 cookies 无效")
            return False
        
        elif response.status_code == 401:
            print("❌ 未授权访问，需要重新登录")
            return False
        
        else:
            print(f"⚠️  意外的状态码: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 网络连接错误")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False


def main():
    """主函数"""
    success = test_login()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 登录测试成功！")
        print("✅ 可以使用当前 cookies 访问 Ideogram AI")
        print("\n💡 下一步:")
        print("   python main.py --test-only    # 完整功能测试")
        print("   python main.py --limit 5      # 尝试下载5张图片")
    else:
        print("❌ 登录测试失败！")
        print("\n🔧 解决方案:")
        print("   1. 检查网络连接")
        print("   2. 更新 config.json 中的 cookies")
        print("   3. 确认 cookies 未过期")
        print("   4. 重新登录 Ideogram AI 获取新的 cookies")
        
        print("\n📝 如何获取新的 cookies:")
        print("   1. 打开浏览器，登录 https://ideogram.ai")
        print("   2. 按 F12 打开开发者工具")
        print("   3. 在 Network 标签页刷新页面")
        print("   4. 找到请求，复制 Cookie 头部信息")
        print("   5. 更新 config.json 文件中的 cookies 字段")


if __name__ == '__main__':
    main()
